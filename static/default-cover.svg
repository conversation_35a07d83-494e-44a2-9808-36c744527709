<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="500" viewBox="0 0 800 500" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bg" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0" style="stop-color:#FFA726; stop-opacity:1" />
      <stop offset="1" style="stop-color:#FF7043; stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="800" height="500" fill="url(#bg)" />

  <!-- 太阳 -->
  <circle cx="700" cy="100" r="50" fill="#FFD54F" />

  <!-- 云朵 -->
  <g fill="white" opacity="0.9">
    <ellipse cx="120" cy="100" rx="50" ry="25" />
    <ellipse cx="160" cy="100" rx="40" ry="20" />
    <ellipse cx="140" cy="85" rx="30" ry="15" />
  </g>

  <!-- 山 -->
  <polygon points="0,400 200,200 400,400" fill="#FF8A65" />
  <polygon points="300,400 500,250 700,400" fill="#F4511E" />
  <polygon points="600,400 750,300 900,400" fill="#E64A19" />

  <!-- 飞机 -->
  <g transform="translate(150, 180) rotate(-10)">
    <path d="M0,0 L60,5 L60,-5 L0,0 Z" fill="white" />
    <rect x="40" y="-2" width="20" height="4" fill="white" />
    <polygon points="10,0 5,8 5,-8" fill="white" />
  </g>

  <!-- 文案 -->
  <text x="50" y="460" font-family="Arial, sans-serif" font-size="48" fill="white" font-weight="bold">
    我的旅行计划
  </text>
</svg>