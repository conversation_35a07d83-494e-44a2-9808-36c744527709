# SVG 显示问题修复

## 问题描述

微信小程序中的 `cover-image` class 会导致 SVG 图片显示异常。根据微信小程序文档：

- SVG 格式不支持百分比单位
- SVG 格式不支持 `<style>` element
- 使用 `mode=scaleToFill` 时，WebView 会居中（除非 SVG 里加上 `preserveAspectRatio="none"`），Skyline 则会撑满

## 修复方案

### 1. 样式修复

**修改 `cover-image` 类样式**：
- 将 `height: auto` 改为固定高度 `height: 300rpx`
- 添加 `object-fit: cover` 替代 mode 属性，提供更好的兼容性

### 2. 图片模式优化

**添加 `getImageMode` 方法**：
```javascript
getImageMode(src) {
    // 对于SVG图片使用scaleToFill模式，其他图片使用aspectFill
    if (src && src.toLowerCase().includes('.svg')) {
        return 'scaleToFill'
    }
    return 'aspectFill'
}
```

**修改 image 标签**：
```html
<!-- 修改前 -->
<image class="cover-image" :src="item.coverImage" mode="aspectFill"></image>

<!-- 修改后 -->
<image class="cover-image" :src="item.coverImage" :mode="getImageMode(item.coverImage)"></image>
```

### 3. SVG 文件优化

**添加 `preserveAspectRatio="none"` 属性**：
- 确保 SVG 在 WebView 中也能正确撑满容器
- 避免居中显示问题

**移除百分比单位**：
- 将渐变定义中的百分比坐标改为绝对值
- `x1="0%" y1="0%" x2="0%" y2="100%"` → `x1="0" y1="0" x2="0" y2="1"`
- `offset="0%"` → `offset="0"`，`offset="100%"` → `offset="1"`

## 修改文件列表

### 页面文件
- `pages/index/index.vue` - 首页瀑布流
- `pages/profile/profile.vue` - 个人中心页面

### SVG 资源文件
- `static/default-cover.svg` - 默认封面图片
- `static/default-avatar.svg` - 默认用户头像
- `static/tabbar/home.svg` - 首页图标
- `static/tabbar/home-active.svg` - 首页激活图标
- `static/tabbar/profile.svg` - 个人中心图标
- `static/tabbar/profile-active.svg` - 个人中心激活图标
- `static/tabbar/add.svg` - 添加图标
- `static/tabbar/add-active.svg` - 添加激活图标
- `static/images/marker.svg` - 地图标记图标

## 技术要点

1. **兼容性优先**：针对 SVG 和普通图片使用不同的显示模式
2. **标准化处理**：统一 SVG 文件格式，避免微信小程序不支持的特性
3. **样式优化**：使用 CSS 属性替代部分 mode 功能，提供更好的跨平台兼容性

## 测试建议

1. 在微信开发者工具中测试 SVG 图片显示
2. 在真机上测试不同尺寸的 SVG 图片
3. 验证瀑布流布局中的图片显示效果
4. 检查个人中心头像和记录封面的显示
