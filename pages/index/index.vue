<template>
	<view class="page">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<view class="navbar-content">
				<text class="app-title">旅行广场</text>
				<view class="nav-actions">
					<button class="nav-btn" @click="goSearch">
						<i class="fas fa-search"></i>
					</button>
					<button class="nav-btn notification-btn" @click="goNotifications">
						<i class="nav-icon fas fa-bell"></i>
						<view v-if="hasUnreadNotifications" class="notification-badge"></view>
					</button>
				</view>
			</view>
		</view>

		<!-- 内容区域 -->
		<scroll-view class="content-scroll" scroll-y="true" @scrolltolower="loadMore">
			<!-- 双列瀑布流 -->
			<view v-if="records.length > 0" class="waterfall-container">
				<view class="waterfall-column">
					<view v-for="item in leftColumnItems" :key="'left-' + item.id"
						  class="waterfall-item" @click="goDetail(item.id)">
						<!-- 封面图片 -->
						<view class="item-cover">
							<image class="cover-image" :src="item.coverImage" :mode="getImageMode(item.coverImage)"></image>
							<view v-if="item.images && item.images.length > 1" class="image-count">
								<i class="count-icon fas fa-camera"></i>
								<text class="count-text">{{ item.images.length }}</text>
							</view>
						</view>

						<!-- 内容信息 -->
						<view class="item-content">
							<text class="item-title">{{ item.title }}</text>
							<view class="item-meta">
								<view class="author-info">
									<image class="author-avatar" :src="item.userAvatar" mode="aspectFill"></image>
									<text class="author-name">{{ item.userName }}</text>
								</view>
								<view class="item-stats">
									<view class="like-count"><i class="fas fa-heart"></i> {{ item.likeCount || 0 }}</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="waterfall-column">
					<view v-for="item in rightColumnItems" :key="'right-' + item.id"
						  class="waterfall-item" @click="goDetail(item.id)">
						<!-- 封面图片 -->
						<view class="item-cover">
							<image class="cover-image" :src="item.coverImage" :mode="getImageMode(item.coverImage)"></image>
							<view v-if="item.images && item.images.length > 1" class="image-count">
								<i class="count-icon fas fa-camera"></i>
								<text class="count-text">{{ item.images.length }}</text>
							</view>
						</view>

						<!-- 内容信息 -->
						<view class="item-content">
							<text class="item-title">{{ item.title }}</text>
							<view class="item-meta">
								<view class="author-info">
									<image class="author-avatar" :src="item.userAvatar" mode="aspectFill"></image>
									<text class="author-name">{{ item.userName }}</text>
								</view>
								<view class="item-stats">
									<view class="like-count"><i class="fas fa-heart"></i> {{ item.likeCount || 0 }}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-else class="empty-state">
				<view class="empty-icon"><i class="fas fa-suitcase-rolling"></i></view>
				<text class="empty-title">还没有旅行记录</text>
				<text class="empty-subtitle">快来成为第一个分享旅行故事的人吧！</text>
				<button class="empty-cta" @click="goRecord">
					<i class="cta-icon fas fa-sparkles"></i>
					<text>开始记录</text>
				</button>
			</view>

			<!-- 加载更多 -->
			<view v-if="hasMore && records.length > 0" class="load-more">
				<view v-if="loading" class="loading">
					<text class="loading-text">加载中...</text>
				</view>
			</view>
		</scroll-view>

		<!-- 自定义底部导航栏 -->
		<custom-tabbar :current="0"></custom-tabbar>
	</view>
</template>

<script>
	import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'

	export default {
		components: {
			CustomTabbar
		},
		data() {
			return {
				records: [], // 从本地存储加载
				ongoingRecord: null, // 未完成的记录对象
				loading: false,
				hasMore: true,
				hasUnreadNotifications: true // 模拟未读通知状态
			}
		},

		computed: {
			// 瀑布流左列数据
			leftColumnItems() {
				return this.processedRecords.filter((_, index) => index % 2 === 0)
			},
			// 瀑布流右列数据
			rightColumnItems() {
				return this.processedRecords.filter((_, index) => index % 2 === 1)
			},
			// 处理后的记录数据
			processedRecords() {
				return this.records.map(record => this.transformRecord(record))
			}
		},

		onShow() {
			// 每次进入首页都从本地加载最新数据
			this.loadRecords()
		},

		methods: {
			loadRecords() {
				const rawRecords = uni.getStorageSync('records') || []
				this.records = rawRecords
			},

			transformRecord(record) {
				// 转换记录数据为瀑布流卡片格式
				return {
					id: record.id,
					title: record.title || '无标题',
					description: record.desc || '',
					userName: record.userName || '旅行者',
					userAvatar: record.userAvatar || '/static/default-avatar.svg',
					coverImage: this.getCoverImage(record),
					images: this.extractImages(record),
					location: record.place || '',
					likeCount: record.likeCount || Math.floor(Math.random() * 50),
					createTime: record.date || new Date().toISOString()
				}
			},

			getCoverImage(record) {
				// 获取封面图片
				if (record.coverImage) return record.coverImage

				const images = this.extractImages(record)
				if (images.length > 0) return images[0]

				return '/static/default-cover.svg'
			},

			extractImages(record) {
				// 从旅行记录中提取图片
				const images = []
				if (record.travel && Array.isArray(record.travel)) {
					record.travel.forEach(city => {
						if (city.points && Array.isArray(city.points)) {
							city.points.forEach(point => {
								if (point.media && Array.isArray(point.media)) {
									images.push(...point.media)
								}
							})
						}
					})
				}
				return images.slice(0, 9) // 最多显示9张图片
			},

			getImageMode(src) {
				// 对于SVG图片使用scaleToFill模式，其他图片使用aspectFill
				if (src && src.toLowerCase().includes('.svg')) {
					return 'scaleToFill'
				}
				return 'aspectFill'
			},

			loadMore() {
				if (this.loading || !this.hasMore) return

				this.loading = true
				// 模拟加载更多数据
				setTimeout(() => {
					this.loading = false
					// 暂时设置为没有更多数据
					this.hasMore = false
				}, 1000)
			},

			goDetail(id) {
				// 直接进入编辑模式
				uni.navigateTo({ url: `/pages/record-new/record-new?id=${id}` })
			},

			goRecord() {
				if (this.ongoingRecord) {
					uni.navigateTo({ url: `/pages/record/record?id=${this.ongoingRecord.id}` })
				} else {
					uni.navigateTo({ url: '/pages/record-new/record-new' })
				}
			},

			goSearch() {
				uni.navigateTo({ url: '/pages/search/search' })
			},

			goNotifications() {
				uni.showToast({ title: '通知功能开发中', icon: 'none' })
			}
		}
	}
</script>

<style>
	page {
		height: 100%;
		width: 100%;
		max-width: 100vw;
		background: #F8F9FA;
		overflow-x: hidden;
		box-sizing: border-box;
	}

	.page {
		min-height: 100vh;
		width: 100%;
		max-width: 100vw;
		background: #F8F9FA;
		overflow-x: hidden;
		box-sizing: border-box;
		/* 为自定义底部导航栏预留空间，包含安全区域 */
		padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
	}

	/* 顶部导航栏 */
	.navbar {
		background: #FFFFFF;
		border-bottom: 1rpx solid #E9ECEF;
		position: sticky;
		top: 0;
		z-index: 100;
		width: 100%;
		max-width: 100%;
		box-sizing: border-box;
	}

	.navbar-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 32rpx;
		height: 88rpx;
		width: 100%;
		max-width: 100%;
		box-sizing: border-box;
	}

	.app-title {
		font-size: 40rpx;
		font-weight: 700;
		color: #FF6B35;
		letter-spacing: -1rpx;
	}

	.nav-actions {
		display: flex;
		gap: 16rpx;
	}

	.nav-btn {
		width: 72rpx;
		height: 72rpx;
		border-radius: 36rpx;
		background: #F8F9FA;
		color: #6C757D;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.2s ease;
	}

	.nav-btn:active {
		background: #E9ECEF;
		transform: scale(0.95);
	}

	/* 搜索按钮特殊样式 */
	.nav-btn:first-child {
		background: #E3F2FD;
		color: #2196F3;
	}

	.nav-btn:first-child:active {
		background: #BBDEFB;
	}

	/* 通知按钮特殊样式 */
	.notification-btn {
		background: #FFF3E0;
		color: #FF9800;
	}

	.notification-btn:active {
		background: #FFE0B2;
	}

	.nav-icon {
		font-size: 32rpx;
	}

	/* 通知按钮样式 */
	.notification-btn {
		position: relative;
	}

	.notification-badge {
		position: absolute;
		top: 8rpx;
		right: 8rpx;
		width: 16rpx;
		height: 16rpx;
		background: #FF4757;
		border-radius: 50%;
		border: 2rpx solid #FFFFFF;
		animation: pulse 2s infinite;
	}

	@keyframes pulse {
		0% {
			transform: scale(1);
			opacity: 1;
		}
		50% {
			transform: scale(1.1);
			opacity: 0.8;
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	/* 内容滚动区域 */
	.content-scroll {
		flex: 1;
		width: 100%;
		max-width: 100%;
		padding: 16rpx 16rpx 0;
		box-sizing: border-box;
		overflow-x: hidden;
	}

	/* 瀑布流容器 */
	.waterfall-container {
		display: flex;
		gap: 16rpx;
		width: 100%;
		max-width: 100%;
		box-sizing: border-box;
	}

	.waterfall-column {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 16rpx;
		min-width: 0; /* 防止flex子项超出容器 */
		max-width: 50%; /* 确保两列不会超出 */
		box-sizing: border-box;
	}

	/* 瀑布流卡片 */
	.waterfall-item {
		background: #FFFFFF;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
		transition: all 0.2s ease;
		width: 100%;
		max-width: 100%;
		box-sizing: border-box;
	}

	.waterfall-item:active {
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
	}

	/* 封面图片区域 */
	.item-cover {
		position: relative;
		width: 100%;
		max-width: 100%;
		box-sizing: border-box;
		overflow: hidden;
	}

	.cover-image {
		width: 100%;
		max-width: 100%;
		height: 300rpx; /* 固定高度，避免SVG显示问题 */
		min-height: 200rpx;
		max-height: 400rpx;
		display: block;
		box-sizing: border-box;
		object-fit: cover; /* 替代mode属性，更好的兼容性 */
	}

	.image-count {
		position: absolute;
		top: 16rpx;
		right: 16rpx;
		background: rgba(0, 0, 0, 0.6);
		color: #FFFFFF;
		padding: 6rpx 12rpx;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		gap: 6rpx;
		font-size: 22rpx;
	}

	.count-icon {
		font-size: 20rpx;
		color: #FFFFFF;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
	}

	.count-text {
		font-size: 22rpx;
		font-weight: 500;
	}

	/* 内容信息区域 */
	.item-content {
		padding: 20rpx;
		width: 100%;
		max-width: 100%;
		box-sizing: border-box;
	}

	.item-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #212529;
		line-height: 1.4;
		margin-bottom: 16rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		max-height: 80rpx;
	}

	.item-meta {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.author-info {
		display: flex;
		align-items: center;
		gap: 12rpx;
		flex: 1;
		min-width: 0;
	}

	.author-avatar {
		width: 48rpx;
		height: 48rpx;
		border-radius: 24rpx;
		border: 2rpx solid #F8F9FA;
		flex-shrink: 0;
	}

	.author-name {
		font-size: 24rpx;
		color: #6C757D;
		font-weight: 500;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.item-stats {
		flex-shrink: 0;
	}

	.like-count {
		font-size: 24rpx;
		color: #FF6B6B;
		font-weight: 500;
	}

	.like-count i {
		margin-right: 6rpx;
	}

	/* 空状态 */
	.empty-state {
		text-align: center;
		padding: 120rpx 48rpx;
		background: #FFFFFF;
		margin: 24rpx 16rpx;
		border-radius: 20rpx;
		width: calc(100% - 32rpx);
		max-width: calc(100% - 32rpx);
		box-sizing: border-box;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}

	.empty-icon {
		font-size: 120rpx;
		margin-bottom: 32rpx;
		color: #ADB5BD;
		animation: float 3s ease-in-out infinite;
	}

	@keyframes float {
		0%, 100% {
			transform: translateY(0);
		}
		50% {
			transform: translateY(-10rpx);
		}
	}

	.empty-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #212529;
		margin-bottom: 16rpx;
	}

	.empty-subtitle {
		font-size: 28rpx;
		color: #6C757D;
		margin-bottom: 48rpx;
		line-height: 1.5;
	}

	.empty-cta {
		background: #FF6B35;
		color: #FFFFFF;
		padding: 24rpx 48rpx;
		border-radius: 32rpx;
		font-size: 28rpx;
		font-weight: 600;
		display: inline-flex;
		align-items: center;
		gap: 12rpx;
		box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.25);
		transition: all 0.2s ease;
	}

	.empty-cta:active {
		transform: scale(0.98);
		background: #E55A2B;
	}

	.cta-icon {
		font-size: 24rpx;
		color: #FFFFFF;
		animation: sparkle 2s ease-in-out infinite;
	}

	@keyframes sparkle {
		0%, 100% {
			opacity: 1;
			transform: scale(1);
		}
		50% {
			opacity: 0.8;
			transform: scale(1.1);
		}
	}

	/* 加载更多 */
	.load-more {
		padding: 32rpx;
		text-align: center;
		width: 100%;
		max-width: 100%;
		box-sizing: border-box;
	}

	.loading-text {
		color: #ADB5BD;
		font-size: 28rpx;
	}


</style>
